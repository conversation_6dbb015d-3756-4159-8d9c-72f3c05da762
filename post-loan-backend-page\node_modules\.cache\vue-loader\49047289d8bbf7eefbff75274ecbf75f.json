{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue", "mtime": 1754299120759}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0TGl0aWdhdGlvbkNvc3RBcHByb3ZhbCwNCiAgZ2V0TGl0aWdhdGlvbkNvc3RTdWJtaXNzaW9uUmVjb3JkcywNCiAgc2luZ2xlQXBwcm92ZUxpdGlnYXRpb25Db3N0TmV3LA0KICBiYXRjaEFwcHJvdmVMaXRpZ2F0aW9uQ29zdE5ldw0KfSBmcm9tICJAL2FwaS9saXRpZ2F0aW9uX2Nvc3RfYXBwcm92YWwvbGl0aWdhdGlvbl9jb3N0X2FwcHJvdmFsIg0KaW1wb3J0IHsgY2hlY2tQZXJtaSB9IGZyb20gIkAvdXRpbHMvcGVybWlzc2lvbiINCmltcG9ydCBhcmVhTGlzdCBmcm9tICIuLi8uLi8uLi9hc3NldHMvYXJlYS5qc29uIg0KaW1wb3J0IHVzZXJJbmZvIGZyb20gJ0AvbGF5b3V0L2NvbXBvbmVudHMvRGlhbG9nL3VzZXJJbmZvLnZ1ZScNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlZtX2Nhcl9vcmRlciIsDQogIGNvbXBvbmVudHM6IHsNCiAgICB1c2VySW5mbywNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOivieiuvOi0ueeUqOWuoeaJueihqOagvOaVsOaNrg0KICAgICAgbGl0aWdhdGlvbkNvc3RBcHByb3ZhbExpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIC8vIOS4peagvOaMieeFpznkuKrnrZvpgInmnaHku7YNCiAgICAgICAgLy8gMS4g6LS35qy+5Lq65aeT5ZCNDQogICAgICAgIGN1c3RvbWVyTmFtZTogbnVsbCwNCiAgICAgICAgLy8gMi4g6LS35qy+5Lq66Lqr5Lu96K+B5Y+3DQogICAgICAgIGNlcnRJZDogbnVsbCwNCiAgICAgICAgLy8gMy4g5Ye65Y2V5rig6YGTDQogICAgICAgIGpnTmFtZTogbnVsbCwNCiAgICAgICAgLy8gNC4g5pS+5qy+6ZO26KGMDQogICAgICAgIGxlbmRpbmdCYW5rOiBudWxsLA0KICAgICAgICAvLyA1LiDms5Xor4nnirbmgIEo5aSa57qnKQ0KICAgICAgICBsaXRpZ2F0aW9uU3RhdHVzOiBudWxsLA0KICAgICAgICAvLyA2LiDnlLPor7fkuroNCiAgICAgICAgYXBwbGljYXRpb25CeTogbnVsbCwNCiAgICAgICAgLy8gNy4g6LS555So57G75Z6LDQogICAgICAgIGNvc3RDYXRlZ29yeTogbnVsbCwNCiAgICAgICAgLy8gOC4g5a6h5om554q25oCBDQogICAgICAgIGFwcHJvdmFsU3RhdHVzOiBudWxsLA0KICAgICAgICAvLyA5LiDnlLPor7fml7bpl7TljLrpl7QNCiAgICAgICAgc3RhcnRUaW1lOiBudWxsLA0KICAgICAgICBlbmRUaW1lOiBudWxsLA0KICAgICAgICAvLyAxMC4g5a6h5om55pe26Ze05Yy66Ze0DQogICAgICAgIGFwcHJvdmFsU3RhcnRUaW1lOiBudWxsLA0KICAgICAgICBhcHByb3ZhbEVuZFRpbWU6IG51bGwsDQogICAgICB9LA0KICAgICAgLy8g5pel5pyf6IyD5Zu0DQogICAgICBkYXRlUmFuZ2U6IFtdLA0KICAgICAgLy8g5a6h5om55pel5pyf6IyD5Zu0DQogICAgICBhcHByb3ZhbERhdGVSYW5nZTogW10sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHsNCiAgICAgICAgaWQ6JycsDQogICAgICAgIHN0YXR1czogMCwNCiAgICAgICAgcmVqZWN0UmVhc29uOm51bGwsDQogICAgICB9LA0KICAgICAgLy8g5b2T5YmN5a6h5om56K6w5b2VDQogICAgICBjdXJyZW50UmVjb3JkOiB7fSwNCiAgICAgIC8vIOi0ueeUqOaPkOS6pOiusOW9leWIl+ihqA0KICAgICAgc3VibWlzc2lvblJlY29yZHM6IFtdLA0KICAgICAgLy8g6K6w5b2V5Yqg6L2954q25oCBDQogICAgICByZWNvcmRzTG9hZGluZzogZmFsc2UsDQogICAgICAvLyDpgInkuK3nmoTorrDlvZUNCiAgICAgIHNlbGVjdGVkUmVjb3JkczogW10sDQogICAgICAvLyDljZXkuKrlrqHmibnlr7nor53moYYNCiAgICAgIHNpbmdsZUFwcHJvdmFsT3BlbjogZmFsc2UsDQogICAgICBzaW5nbGVBcHByb3ZhbEZvcm06IHsNCiAgICAgICAgaWQ6ICcnLA0KICAgICAgICBhY3Rpb246ICcnLCAvLyAnYXBwcm92ZScg5oiWICdyZWplY3QnDQogICAgICAgIHJlamVjdFJlYXNvbjogJycNCiAgICAgIH0sDQogICAgICAvLyDmibnph4/lrqHmibnlr7nor53moYYNCiAgICAgIGJhdGNoQXBwcm92YWxPcGVuOiBmYWxzZSwNCiAgICAgIGJhdGNoQXBwcm92YWxGb3JtOiB7DQogICAgICAgIGFjdGlvbjogJycsIC8vICdhcHByb3ZlJyDmiJYgJ3JlamVjdCcNCiAgICAgICAgcmVqZWN0UmVhc29uOiAnJw0KICAgICAgfSwNCiAgICAgIC8vIOi0t+asvuS6uuS/oeaBr+ebuOWFsw0KICAgICAgdXNlckluZm9WaXNpYmxlOiBmYWxzZSwNCiAgICAgIGN1c3RvbWVySW5mbzoge30sDQogICAgICAvLyDms5Xor4nnirbmgIHlpJrnuqfpgInpobkNCiAgICAgIGxpdGlnYXRpb25TdGF0dXNPcHRpb25zOiBbDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogJ+i1t+iviScsDQogICAgICAgICAgbGFiZWw6ICfotbfor4knLA0KICAgICAgICAgIGNoaWxkcmVuOiBbDQogICAgICAgICAgICB7IHZhbHVlOiAn6LW36K+JLeWHhuWkh+adkOaWmScsIGxhYmVsOiAn5YeG5aSH5p2Q5paZJyB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogJ+i1t+iviS3lt7Lmj5DkuqQnLCBsYWJlbDogJ+W3suaPkOS6pCcgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6ICfotbfor4kt5rOV6Zmi5Y+X55CGJywgbGFiZWw6ICfms5XpmaLlj5fnkIYnIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogJ+WuoeeQhicsDQogICAgICAgICAgbGFiZWw6ICflrqHnkIYnLA0KICAgICAgICAgIGNoaWxkcmVuOiBbDQogICAgICAgICAgICB7IHZhbHVlOiAn5a6h55CGLeW8gOW6reWuoeeQhicsIGxhYmVsOiAn5byA5bqt5a6h55CGJyB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogJ+WuoeeQhi3nrYnlvoXliKTlhrMnLCBsYWJlbDogJ+etieW+heWIpOWGsycgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6ICflrqHnkIYt5LiA5a6h5Yik5YazJywgbGFiZWw6ICfkuIDlrqHliKTlhrMnIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogJ+aJp+ihjCcsDQogICAgICAgICAgbGFiZWw6ICfmiafooYwnLA0KICAgICAgICAgIGNoaWxkcmVuOiBbDQogICAgICAgICAgICB7IHZhbHVlOiAn5omn6KGMLeeUs+ivt+aJp+ihjCcsIGxhYmVsOiAn55Sz6K+35omn6KGMJyB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogJ+aJp+ihjC3miafooYzkuK0nLCBsYWJlbDogJ+aJp+ihjOS4rScgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6ICfmiafooYwt5omn6KGM5a6M5q+VJywgbGFiZWw6ICfmiafooYzlrozmr5UnIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogJ+e7k+ahiCcsDQogICAgICAgICAgbGFiZWw6ICfnu5PmoYgnLA0KICAgICAgICAgIGNoaWxkcmVuOiBbDQogICAgICAgICAgICB7IHZhbHVlOiAn57uT5qGILeiDnOiviee7k+ahiCcsIGxhYmVsOiAn6IOc6K+J57uT5qGIJyB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogJ+e7k+ahiC3otKXor4nnu5PmoYgnLCBsYWJlbDogJ+i0peiviee7k+ahiCcgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6ICfnu5PmoYgt5ZKM6Kej57uT5qGIJywgbGFiZWw6ICflkozop6Pnu5PmoYgnIH0NCiAgICAgICAgICBdDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGtleVByb3ZpbmNlOicnLA0KICAgICAgICBrZXlDaXR5OicnLA0KICAgICAgICBrZXlCb3JvdWdoOicnLA0KICAgICAgICBrZXlEZXRhaWxBZGRyZXNzOicnLA0KICAgICAgfSwNCiAgICAgIHByb3ZpbmNlTGlzdDphcmVhTGlzdCwNCiAgICAgIGNpdHlMaXN0OltdLA0KICAgICAgZGlzdHJpY3RMaXN0OltdDQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i5rOV6K+J6LS555So5a6h5om55YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGxpc3RMaXRpZ2F0aW9uQ29zdEFwcHJvdmFsKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmxpdGlnYXRpb25Db3N0QXBwcm92YWxMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5p+l6K+i5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICB0aGlzLnJlc2V0KCkNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDonJywNCiAgICAgICAgc3RhdHVzOiAwLA0KICAgICAgICByZWplY3RSZWFzb246bnVsbCwNCiAgICAgIH0NCiAgICAgIHRoaXMuY3VycmVudFJlY29yZCA9IHt9DQogICAgICB0aGlzLnN1Ym1pc3Npb25SZWNvcmRzID0gW10NCiAgICAgIHRoaXMuc2VsZWN0ZWRSZWNvcmRzID0gW10NCiAgICAgIHRoaXMuc2luZ2xlQXBwcm92YWxPcGVuID0gZmFsc2UNCiAgICAgIHRoaXMuYmF0Y2hBcHByb3ZhbE9wZW4gPSBmYWxzZQ0KICAgICAgdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0gPSB7DQogICAgICAgIGlkOiAnJywNCiAgICAgICAgYWN0aW9uOiAnJywNCiAgICAgICAgcmVqZWN0UmVhc29uOiAnJw0KICAgICAgfQ0KICAgICAgdGhpcy5iYXRjaEFwcHJvdmFsRm9ybSA9IHsNCiAgICAgICAgYWN0aW9uOiAnJywNCiAgICAgICAgcmVqZWN0UmVhc29uOiAnJw0KICAgICAgfQ0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKQ0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMQ0KICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXQ0KICAgICAgdGhpcy5hcHByb3ZhbERhdGVSYW5nZSA9IFtdDQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIikNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqBWSUVXIg0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudFJlY29yZCA9IHJvdw0KICAgICAgdGhpcy5sb2FkU3VibWlzc2lvblJlY29yZHMocm93LmxpdGlnYXRpb25DYXNlSWQpDQogICAgICB0aGlzLm9wZW4gPSB0cnVlDQogICAgfSwNCg0KICAgIC8qKiDmibnph4/kv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVCYXRjaEVkaXQoKSB7DQogICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign6K+36YCJ5oup5Y2V5p2h6K6w5b2V6L+b6KGM5a6h5om55pON5L2cJykNCiAgICB9LA0KDQogICAgLyoqIOWKoOi9vei0ueeUqOaPkOS6pOiusOW9lSAqLw0KICAgIGxvYWRTdWJtaXNzaW9uUmVjb3JkcyhsaXRpZ2F0aW9uQ2FzZUlkKSB7DQogICAgICB0aGlzLnJlY29yZHNMb2FkaW5nID0gdHJ1ZQ0KICAgICAgZ2V0TGl0aWdhdGlvbkNvc3RTdWJtaXNzaW9uUmVjb3JkcyhsaXRpZ2F0aW9uQ2FzZUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5zdWJtaXNzaW9uUmVjb3JkcyA9IHJlc3BvbnNlLmRhdGEgfHwgW10NCiAgICAgICAgdGhpcy5yZWNvcmRzTG9hZGluZyA9IGZhbHNlDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMucmVjb3Jkc0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLyoqIOiusOW9lemAieaLqeWPmOWMliAqLw0KICAgIGhhbmRsZVJlY29yZFNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRSZWNvcmRzID0gc2VsZWN0aW9uDQogICAgfSwNCg0KICAgIC8qKiDljZXkuKrlrqHmibkgKi8NCiAgICBoYW5kbGVTaW5nbGVBcHByb3ZlKHJlY29yZCwgYWN0aW9uKSB7DQogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5pZCA9IHJlY29yZC5pZA0KICAgICAgdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0uYWN0aW9uID0gYWN0aW9uDQogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5yZWplY3RSZWFzb24gPSAnJw0KICAgICAgdGhpcy5zaW5nbGVBcHByb3ZhbE9wZW4gPSB0cnVlDQogICAgfSwNCg0KICAgIC8qKiDnoa7orqTljZXkuKrlrqHmibkgKi8NCiAgICBjb25maXJtU2luZ2xlQXBwcm92YWwoKSB7DQogICAgICBpZiAodGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0uYWN0aW9uID09PSAncmVqZWN0Jykgew0KICAgICAgICB0aGlzLiRyZWZzWyJzaW5nbGVBcHByb3ZhbEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgICAgaWYgKCF2YWxpZCkgcmV0dXJuDQogICAgICAgICAgdGhpcy5leGVjdXRlU2luZ2xlQXBwcm92YWwoKQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5leGVjdXRlU2luZ2xlQXBwcm92YWwoKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5omn6KGM5Y2V5Liq5a6h5om5ICovDQogICAgZXhlY3V0ZVNpbmdsZUFwcHJvdmFsKCkgew0KICAgICAgY29uc3QgZGF0YSA9IHsNCiAgICAgICAgaWQ6IHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtLmlkLA0KICAgICAgICBhY3Rpb246IHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtLmFjdGlvbiwNCiAgICAgICAgcmVqZWN0UmVhc29uOiB0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5yZWplY3RSZWFzb24NCiAgICAgIH0NCg0KICAgICAgc2luZ2xlQXBwcm92ZUxpdGlnYXRpb25Db3N0TmV3KGRhdGEpLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKGAke3RoaXMuc2luZ2xlQXBwcm92YWxGb3JtLmFjdGlvbiA9PT0gJ2FwcHJvdmUnID8gJ+mAmui/hycgOiAn5ouS57udJ33lrqHmibnmiJDlip9gKQ0KICAgICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsT3BlbiA9IGZhbHNlDQogICAgICAgIHRoaXMubG9hZFN1Ym1pc3Npb25SZWNvcmRzKHRoaXMuY3VycmVudFJlY29yZC5saXRpZ2F0aW9uQ2FzZUlkKQ0KICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgfSkuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WuoeaJueWksei0pTonLCBlcnJvcikNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8qKiDmibnph4/lrqHmibkgKi8NCiAgICBoYW5kbGVCYXRjaEFwcHJvdmUoYWN0aW9uKSB7DQogICAgICBpZiAodGhpcy5zZWxlY3RlZFJlY29yZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fpgInmi6nopoHlrqHmibnnmoTorrDlvZUnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy5iYXRjaEFwcHJvdmFsRm9ybS5hY3Rpb24gPSBhY3Rpb24NCiAgICAgIHRoaXMuYmF0Y2hBcHByb3ZhbEZvcm0ucmVqZWN0UmVhc29uID0gJycNCiAgICAgIHRoaXMuYmF0Y2hBcHByb3ZhbE9wZW4gPSB0cnVlDQogICAgfSwNCg0KICAgIC8qKiDnoa7orqTmibnph4/lrqHmibkgKi8NCiAgICBjb25maXJtQmF0Y2hBcHByb3ZhbCgpIHsNCiAgICAgIGlmICh0aGlzLmJhdGNoQXBwcm92YWxGb3JtLmFjdGlvbiA9PT0gJ3JlamVjdCcpIHsNCiAgICAgICAgdGhpcy4kcmVmc1siYmF0Y2hBcHByb3ZhbEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgICAgaWYgKCF2YWxpZCkgcmV0dXJuDQogICAgICAgICAgdGhpcy5leGVjdXRlQmF0Y2hBcHByb3ZhbCgpDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmV4ZWN1dGVCYXRjaEFwcHJvdmFsKCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOaJp+ihjOaJuemHj+WuoeaJuSAqLw0KICAgIGV4ZWN1dGVCYXRjaEFwcHJvdmFsKCkgew0KICAgICAgY29uc3QgZGF0YSA9IHsNCiAgICAgICAgaWRzOiB0aGlzLnNlbGVjdGVkUmVjb3Jkcy5tYXAocmVjb3JkID0+IHJlY29yZC5pZCksDQogICAgICAgIGFjdGlvbjogdGhpcy5iYXRjaEFwcHJvdmFsRm9ybS5hY3Rpb24sDQogICAgICAgIHJlamVjdFJlYXNvbjogdGhpcy5iYXRjaEFwcHJvdmFsRm9ybS5yZWplY3RSZWFzb24NCiAgICAgIH0NCg0KICAgICAgYmF0Y2hBcHByb3ZlTGl0aWdhdGlvbkNvc3ROZXcoZGF0YSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoYOaJuemHjyR7dGhpcy5iYXRjaEFwcHJvdmFsRm9ybS5hY3Rpb24gPT09ICdhcHByb3ZlJyA/ICfpgJrov4cnIDogJ+aLkue7nSd95a6h5om55oiQ5YqfYCkNCiAgICAgICAgdGhpcy5iYXRjaEFwcHJvdmFsT3BlbiA9IGZhbHNlDQogICAgICAgIHRoaXMuc2VsZWN0ZWRSZWNvcmRzID0gW10NCiAgICAgICAgdGhpcy5sb2FkU3VibWlzc2lvblJlY29yZHModGhpcy5jdXJyZW50UmVjb3JkLmxpdGlnYXRpb25DYXNlSWQpDQogICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICB9KS5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5om56YeP5a6h5om55aSx6LSlOicsIGVycm9yKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLyoqIOS4u+WIl+ihqOaJuemHj+WuoeaJuSAqLw0KICAgIGhhbmRsZUJhdGNoQXBwcm92ZU1haW4oc3RhdHVzKSB7DQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fpgInmi6nopoHlrqHmibnnmoTorrDlvZUnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3Qgc3RhdHVzVGV4dCA9IHN0YXR1cyA9PT0gJzAnID8gJ+mAmui/hycgOiAn5ouS57udJw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybShg56Gu6K6k6KaB5om56YePJHtzdGF0dXNUZXh0femAieS4reeahCAke3RoaXMuaWRzLmxlbmd0aH0g5p2h6K6w5b2V5ZCX77yfYCkudGhlbigoKSA9PiB7DQogICAgICAgIGNvbnN0IGRhdGEgPSB7DQogICAgICAgICAgaWRzOiB0aGlzLmlkcywNCiAgICAgICAgICBhY3Rpb246IHN0YXR1cyA9PT0gJzAnID8gJ2FwcHJvdmUnIDogJ3JlamVjdCcsDQogICAgICAgICAgcmVqZWN0UmVhc29uOiBzdGF0dXMgPT09ICcxJyA/ICfmibnph4/mi5Lnu50nIDogJycNCiAgICAgICAgfQ0KDQogICAgICAgIHJldHVybiBiYXRjaEFwcHJvdmVMaXRpZ2F0aW9uQ29zdE5ldyhkYXRhKQ0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoYOaJuemHjyR7c3RhdHVzVGV4dH3miJDlip9gKQ0KICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCg0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUoKSB7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpgInkuK3nmoTmlbDmja7pobnvvJ8nKS50aGVuKCgpID0+IHsNCiAgICAgICAgLy8g6L+Z6YeM5Y+v5Lul6LCD55So5Yig6ZmkQVBJ77yM5pqC5pe25Y+q5piv5o+Q56S6DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOWKn+iDveaaguacquWunueOsCIpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KDQoNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbGl0aWdhdGlvbl9jb3N0X2FwcHJvdmFsL2xpdGlnYXRpb25fY29zdF9hcHByb3ZhbC9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGBsaXRpZ2F0aW9uX2Nvc3RfYXBwcm92YWxfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCg0KICAgIC8qKiDmiZPlvIDotLfmrL7kurrkv6Hmga8gKi8NCiAgICBvcGVuVXNlckluZm8ocm93KSB7DQogICAgICBpZiAoIXJvdy5jdXN0b21lcklkICYmICFyb3cuYXBwbHlJZCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5peg5rOV6I635Y+W6LS35qy+5Lq65L+h5oGvJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIHRoaXMuY3VzdG9tZXJJbmZvID0gew0KICAgICAgICBjdXN0b21lcklkOiByb3cuY3VzdG9tZXJJZCwNCiAgICAgICAgYXBwbHlJZDogcm93LmFwcGx5SWQsDQogICAgICAgIGN1c3RvbWVyTmFtZTogcm93LmN1c3RvbWVyTmFtZQ0KICAgICAgfQ0KICAgICAgdGhpcy51c2VySW5mb1Zpc2libGUgPSB0cnVlDQogICAgfSwNCg0KICAgIC8qKiDlpITnkIbml6XmnJ/ojIPlm7Tlj5jljJYgKi8NCiAgICBoYW5kbGVEYXRlUmFuZ2VDaGFuZ2UoZGF0ZXMpIHsNCiAgICAgIGlmIChkYXRlcyAmJiBkYXRlcy5sZW5ndGggPT09IDIpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdGFydFRpbWUgPSBkYXRlc1swXQ0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmVuZFRpbWUgPSBkYXRlc1sxXQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdGFydFRpbWUgPSBudWxsDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kVGltZSA9IG51bGwNCiAgICAgIH0NCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQoNCiAgICAvKiog5aSE55CG5a6h5om55pe26Ze06IyD5Zu05Y+Y5YyWICovDQogICAgaGFuZGxlQXBwcm92YWxEYXRlUmFuZ2VDaGFuZ2UoZGF0ZXMpIHsNCiAgICAgIGlmIChkYXRlcyAmJiBkYXRlcy5sZW5ndGggPT09IDIpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hcHByb3ZhbFN0YXJ0VGltZSA9IGRhdGVzWzBdDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXBwcm92YWxFbmRUaW1lID0gZGF0ZXNbMV0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXBwcm92YWxTdGFydFRpbWUgPSBudWxsDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXBwcm92YWxFbmRUaW1lID0gbnVsbA0KICAgICAgfQ0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgfSwNCg0KICAgIC8qKiDojrflj5bnirbmgIHmlofmnKwgKi8NCiAgICBnZXRTdGF0dXNUZXh0KHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAnMCc6ICfmnKrlrqHmibknLA0KICAgICAgICAnMSc6ICflhajpg6jlkIzmhI8nLA0KICAgICAgICAnMic6ICflt7Lmi5Lnu50nLA0KICAgICAgICAnMyc6ICfms5Xor4nkuLvnrqHlrqHmibknLA0KICAgICAgICAnNCc6ICfmgLvnm5HlrqHmibknLA0KICAgICAgICAnNSc6ICfotKLliqHkuLvnrqEv5oC755uR5oqE6YCBJywNCiAgICAgICAgJzYnOiAn5oC757uP55CGL+iRo+S6i+mVv+WuoeaJuScNCiAgICAgIH0NCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAn5pyq55+l54q25oCBJw0KICAgIH0sDQoNCiAgICAvKiog5qOA5p+l6K6w5b2V5piv5ZCm5Y+v5Lul5a6h5om5ICovDQogICAgY2FuQXBwcm92ZVJlY29yZChyZWNvcmQpIHsNCiAgICAgIC8vIOmmluWFiOajgOafpeeUqOaIt+aYr+WQpuacieWuoeaJueadg+mZkA0KICAgICAgaWYgKCFjaGVja1Blcm1pKFsnbGl0aWdhdGlvbl9jb3N0X2FwcHJvdmFsOmxpdGlnYXRpb25fY29zdF9hcHByb3ZhbDphcHByb3ZlJ10pKSB7DQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KDQogICAgICAvLyDlt7LlrozmiJDnmoTnirbmgIHkuI3og73lho3lrqHmibkNCiAgICAgIGlmIChyZWNvcmQuYXBwcm92YWxTdGF0dXMgPT0gJzEnIHx8IHJlY29yZC5hcHByb3ZhbFN0YXR1cyA9PSAnMicpIHsNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQoNCiAgICAgIC8vIOajgOafpeeUqOaIt+inkuiJsuaYr+WQpuWMuemFjeW9k+WJjeWuoeaJueeKtuaAgQ0KICAgICAgcmV0dXJuIHRoaXMuY2FuVXNlckFwcHJvdmVTdGF0dXMocmVjb3JkLmFwcHJvdmFsU3RhdHVzKQ0KICAgIH0sDQoNCiAgICAvKiog5qOA5p+l55So5oi36KeS6Imy5piv5ZCm5Y+v5Lul5a6h5om55b2T5YmN54q25oCBICovDQogICAgY2FuVXNlckFwcHJvdmVTdGF0dXMoc3RhdHVzKSB7DQogICAgICBjb25zdCByb2xlcyA9IHRoaXMuJHN0b3JlLmdldHRlcnMucm9sZXMgfHwgW10NCg0KICAgICAgLy8g6L+H5ruk5o6JVnVl55qET2JzZXJ2ZXLlr7nosaHvvIzlj6rkv53nlZnlrZfnrKbkuLLop5LoibINCiAgICAgIGNvbnN0IGNsZWFuUm9sZXMgPSByb2xlcy5maWx0ZXIocm9sZSA9PiB0eXBlb2Ygcm9sZSA9PT0gJ3N0cmluZycpDQoNCiAgICAgIC8vIOeuoeeQhuWRmOaLpeacieaJgOacieadg+mZkA0KICAgICAgaWYgKGNsZWFuUm9sZXMuaW5jbHVkZXMoJ2FkbWluJykpIHsNCiAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgIH0NCg0KICAgICAgLy8g5qOA5p+l6KeS6Imy5ZCN56ew5piv5ZCm5Yy56YWNDQogICAgICBjb25zdCBoYXNSb2xlID0gKHRhcmdldFJvbGUpID0+IHsNCiAgICAgICAgcmV0dXJuIGNsZWFuUm9sZXMuc29tZShyb2xlID0+IHJvbGUgPT09IHRhcmdldFJvbGUpDQogICAgICB9DQoNCiAgICAgIC8vIOWkhOeQhuepuueKtuaAge+8iG51bGzjgIF1bmRlZmluZWTjgIHnqbrlrZfnrKbkuLLvvIkNCiAgICAgIGlmIChzdGF0dXMgPT0gbnVsbCB8fCBzdGF0dXMgPT09IHVuZGVmaW5lZCB8fCBzdGF0dXMgPT09ICcnKSB7DQogICAgICAgIHJldHVybiBoYXNSb2xlKCfms5Xor4nkuLvnrqEnKSB8fCBoYXNSb2xlKCdsaXRpZ2F0aW9uX3N1cGVydmlzb3InKSB8fCBoYXNSb2xlKCdqdWRpY2lhbF9kaXJlY3RvcicpDQogICAgICB9DQoNCiAgICAgIGxldCByZXN1bHQgPSBmYWxzZQ0KICAgICAgc3dpdGNoIChzdGF0dXMpIHsNCiAgICAgICAgY2FzZSAnMCc6IC8vIOacquWuoeaJuQ0KICAgICAgICAgIC8vIOacquWuoeaJueeKtuaAgeeUseazleivieS4u+euoeWuoeaJuQ0KICAgICAgICAgIHJlc3VsdCA9IGhhc1JvbGUoJ+azleivieS4u+euoScpIHx8IGhhc1JvbGUoJ2xpdGlnYXRpb25fc3VwZXJ2aXNvcicpIHx8IGhhc1JvbGUoJ2p1ZGljaWFsX2RpcmVjdG9yJykNCiAgICAgICAgICBicmVhaw0KICAgICAgICBjYXNlICczJzogLy8g5rOV6K+J5Li7566h5a6h5om554q25oCBDQogICAgICAgICAgLy8g5rOV6K+J5Li7566h5a6h5om554q25oCB55Sx5oC755uR5a6h5om5DQogICAgICAgICAgcmVzdWx0ID0gaGFzUm9sZSgn5oC755uRJykgfHwgaGFzUm9sZSgnZGlyZWN0b3InKQ0KICAgICAgICAgIGJyZWFrDQogICAgICAgIGNhc2UgJzQnOiAvLyDmgLvnm5HlrqHmibnnirbmgIENCiAgICAgICAgICAvLyDmgLvnm5HlrqHmibnnirbmgIHnlLHotKLliqHkuLvnrqEv5oC755uR5a6h5om5DQogICAgICAgICAgcmVzdWx0ID0gaGFzUm9sZSgn6LSi5Yqh5Li7566hJykgfHwgaGFzUm9sZSgn6LSi5Yqh5oC755uRJykgfHwgaGFzUm9sZSgnZmluYW5jZV9zdXBlcnZpc29yJykgfHwgaGFzUm9sZSgnZmluYW5jZV9kaXJlY3RvcicpDQogICAgICAgICAgYnJlYWsNCiAgICAgICAgY2FzZSAnNSc6IC8vIOi0ouWKoeS4u+euoS/mgLvnm5HmioTpgIHnirbmgIENCiAgICAgICAgICAvLyDotKLliqHkuLvnrqEv5oC755uR5oqE6YCB54q25oCB55Sx5oC757uP55CGL+iRo+S6i+mVv+WuoeaJuQ0KICAgICAgICAgIHJlc3VsdCA9IGhhc1JvbGUoJ+aAu+e7j+eQhicpIHx8IGhhc1JvbGUoJ+iRo+S6i+mVvycpIHx8IGhhc1JvbGUoJ2dlbmVyYWxfbWFuYWdlcicpIHx8IGhhc1JvbGUoJ2NoYWlybWFuJykNCiAgICAgICAgICBicmVhaw0KICAgICAgICBjYXNlICc2JzogLy8g5oC757uP55CGL+iRo+S6i+mVv+WuoeaJueeKtuaAgQ0KICAgICAgICAgIC8vIOaAu+e7j+eQhi/okaPkuovplb/lrqHmibnnirbmgIHlt7Lnu4/mmK/mnIDlkI7kuIDnuqfvvIzkuI3lhYHorrjlho3lrqHmibkNCiAgICAgICAgICByZXN1bHQgPSBmYWxzZQ0KICAgICAgICAgIGJyZWFrDQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgcmVzdWx0ID0gZmFsc2UNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHJlc3VsdA0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5b2T5YmN54q25oCB6ZyA6KaB55qE5a6h5om56KeS6Imy5paH5pysICovDQogICAgZ2V0UmVxdWlyZWRSb2xlVGV4dChzdGF0dXMpIHsNCiAgICAgIHN3aXRjaCAoc3RhdHVzKSB7DQogICAgICAgIGNhc2UgJzAnOiAvLyDmnKrlrqHmibkNCiAgICAgICAgY2FzZSBudWxsOg0KICAgICAgICBjYXNlICcnOg0KICAgICAgICAgIHJldHVybiAn5rOV6K+J5Li7566hJw0KICAgICAgICBjYXNlICczJzogLy8g5rOV6K+J5Li7566h5a6h5om554q25oCBDQogICAgICAgICAgcmV0dXJuICfmgLvnm5EnDQogICAgICAgIGNhc2UgJzQnOiAvLyDmgLvnm5HlrqHmibnnirbmgIENCiAgICAgICAgICByZXR1cm4gJ+i0ouWKoeS4u+euoS/mgLvnm5EnDQogICAgICAgIGNhc2UgJzUnOiAvLyDotKLliqHkuLvnrqEv5oC755uR5oqE6YCB54q25oCBDQogICAgICAgICAgcmV0dXJuICfmgLvnu4/nkIYv6JGj5LqL6ZW/Jw0KICAgICAgICBjYXNlICc2JzogLy8g5oC757uP55CGL+iRo+S6i+mVv+WuoeaJueeKtuaAgQ0KICAgICAgICAgIHJldHVybiAn5bey5a6M5oiQJw0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIHJldHVybiAn5pyq55+lJw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["litigation_approval.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "litigation_approval.vue", "sourceRoot": "src/views/litigation/litigation", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <!-- 1. 贷款人姓名 -->\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input\r\n          v-model=\"queryParams.customerName\"\r\n          placeholder=\"贷款人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 2. 贷款人身份证号 -->\r\n      <el-form-item label=\"\" prop=\"certId\">\r\n        <el-input\r\n          v-model=\"queryParams.certId\"\r\n          placeholder=\"贷款人身份证号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 3. 出单渠道 -->\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input\r\n          v-model=\"queryParams.jgName\"\r\n          placeholder=\"出单渠道\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 4. 放款银行 -->\r\n      <el-form-item label=\"\" prop=\"lendingBank\">\r\n        <el-input\r\n          v-model=\"queryParams.lendingBank\"\r\n          placeholder=\"放款银行\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 5. 法诉状态(需呈现多级) -->\r\n      <el-form-item label=\"\" prop=\"litigationStatus\">\r\n        <el-cascader\r\n          v-model=\"queryParams.litigationStatus\"\r\n          :options=\"litigationStatusOptions\"\r\n          :props=\"{ expandTrigger: 'hover', value: 'value', label: 'label', children: 'children' }\"\r\n          placeholder=\"法诉状态\"\r\n          clearable\r\n          @change=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 6. 申请人 -->\r\n      <el-form-item label=\"\" prop=\"applicationBy\">\r\n        <el-input\r\n          v-model=\"queryParams.applicationBy\"\r\n          placeholder=\"申请人\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 7. 费用类型 -->\r\n      <el-form-item label=\"\" prop=\"costCategory\">\r\n        <el-select v-model=\"queryParams.costCategory\" placeholder=\"费用类型\" clearable @change=\"handleQuery\">\r\n          <el-option label=\"律师费\" value=\"律师费\" />\r\n          <el-option label=\"诉讼费\" value=\"诉讼费\" />\r\n          <el-option label=\"保全费\" value=\"保全费\" />\r\n          <el-option label=\"执行费\" value=\"执行费\" />\r\n          <el-option label=\"其他费用\" value=\"其他费用\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <!-- 8. 审批状态 -->\r\n      <el-form-item label=\"\" prop=\"approvalStatus\">\r\n        <el-select v-model=\"queryParams.approvalStatus\" placeholder=\"审批状态\" clearable @change=\"handleQuery\">\r\n          <el-option label=\"未审批\" value=\"0\" />\r\n          <el-option label=\"全部同意\" value=\"1\" />\r\n          <el-option label=\"已拒绝\" value=\"2\" />\r\n          <el-option label=\"法诉主管审批\" value=\"3\" />\r\n          <el-option label=\"总监审批\" value=\"4\" />\r\n          <el-option label=\"财务主管/总监抄送\" value=\"5\" />\r\n          <el-option label=\"总经理/董事长审批\" value=\"6\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <!-- 9. 申请时间区间 -->\r\n      <el-form-item label=\"\" prop=\"dateRange\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"申请开始日期\"\r\n          end-placeholder=\"申请结束日期\"\r\n          format=\"yyyy-MM-dd\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"handleDateRangeChange\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 10. 审批时间区间 -->\r\n      <el-form-item label=\"\" prop=\"approvalDateRange\">\r\n        <el-date-picker\r\n          v-model=\"approvalDateRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"审批开始日期\"\r\n          end-placeholder=\"审批结束日期\"\r\n          format=\"yyyy-MM-dd\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"handleApprovalDateRangeChange\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleBatchEdit\"\r\n          v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"litigationCostApprovalList\" @selection-change=\"handleSelectionChange\" row-key=\"id\" style=\"width: 100%\" flex=\"right\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"60\" fixed />\r\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n      <el-table-column label=\"最新申请时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n      <el-table-column label=\"案件负责人\" align=\"center\" prop=\"curator\" width=\"100\" />\r\n      <el-table-column label=\"提交次数\" align=\"center\" prop=\"submissionCount\" width=\"100\" />\r\n      <el-table-column label=\"法诉状态\" align=\"center\" prop=\"litigationStatus\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{scope.row.litigationStatus == '1'?'待立案':'已邮寄'}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"贷款人\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            type=\"text\"\r\n            @click=\"openUserInfo(scope.row)\"\r\n            style=\"color: #409EFF;\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\" />\r\n      <el-table-column label=\"地区\" align=\"center\" prop=\"area\" width=\"80\" />\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"lendingBank\" />\r\n      <el-table-column label=\"法院地\" align=\"center\" prop=\"courtLocation\" />\r\n      <el-table-column label=\"诉讼法院\" align=\"center\" prop=\"commonPleas\" />\r\n      <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\" />\r\n      <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\" />\r\n      <el-table-column label=\"保险费\" align=\"center\" prop=\"insurance\" width=\"80\" />\r\n      <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\" />\r\n      <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\" />\r\n      <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\" />\r\n      <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\" />\r\n      <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\" />\r\n      <el-table-column label=\"特殊通道费\" align=\"center\" prop=\"specialChannelFees\" width=\"100\" />\r\n      <el-table-column label=\"日常报销\" align=\"center\" prop=\"otherAmountsOwed\" width=\"80\" />\r\n      <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n      <el-table-column label=\"整体审批状态\" align=\"center\" prop=\"overallApprovalStatus\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.overallApprovalStatus == 'NOT_STARTED'\" type=\"info\">未开始审批</el-tag>\r\n          <el-tag v-else-if=\"scope.row.overallApprovalStatus == 'PARTIAL'\" type=\"warning\">部分审批</el-tag>\r\n          <el-tag v-else-if=\"scope.row.overallApprovalStatus == 'COMPLETED'\" type=\"success\">全部完成</el-tag>\r\n          <el-tag v-else type=\"info\">{{ scope.row.overallApprovalStatus || '未知状态' }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\" />\r\n      <el-table-column label=\"审批人角色\" align=\"center\" prop=\"approveRole\" />\r\n      <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"100\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:approve']\"\r\n          >审批</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 审批对话框 -->\r\n    <el-dialog title=\"法诉费用审批详情\" :visible.sync=\"open\" width=\"1200px\" append-to-body>\r\n      <div class=\"approval-header\">\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <strong>贷款人：</strong>{{ currentRecord.customerName }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>案件负责人：</strong>{{ currentRecord.curator }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>法院地：</strong>{{ currentRecord.courtLocation }}\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <div class=\"batch-approval-section\" style=\"margin: 20px 0;\">\r\n        <el-button\r\n          type=\"success\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('approve')\"\r\n          v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:approve']\">\r\n          批量通过 ({{ selectedRecords.length }})\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('reject')\"\r\n          v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:approve']\">\r\n          批量拒绝 ({{ selectedRecords.length }})\r\n        </el-button>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"submissionRecords\"\r\n        @selection-change=\"handleRecordSelectionChange\"\r\n        v-loading=\"recordsLoading\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" fixed=\"left\" />\r\n        <el-table-column label=\"提交时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n        <el-table-column label=\"提交人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n        <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\" />\r\n        <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\" />\r\n        <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\" />\r\n        <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\" />\r\n        <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\" />\r\n        <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\" />\r\n        <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\" />\r\n        <el-table-column label=\"违约金\" align=\"center\" prop=\"penalty\" width=\"80\" />\r\n        <el-table-column label=\"担保费\" align=\"center\" prop=\"guaranteeFee\" width=\"80\" />\r\n        <el-table-column label=\"居间费\" align=\"center\" prop=\"intermediaryFee\" width=\"80\" />\r\n        <el-table-column label=\"代偿金\" align=\"center\" prop=\"compensity\" width=\"80\" />\r\n        <el-table-column label=\"判决金额\" align=\"center\" prop=\"judgmentAmount\" width=\"100\" />\r\n        <el-table-column label=\"利息\" align=\"center\" prop=\"interest\" width=\"80\" />\r\n        <el-table-column label=\"其他欠款\" align=\"center\" prop=\"otherAmountsOwed\" width=\"100\" />\r\n        <el-table-column label=\"保险费\" align=\"center\" prop=\"insurance\" width=\"80\" />\r\n        <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n        <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.approvalStatus == '0' || scope.row.approvalStatus == null || scope.row.approvalStatus == ''\" type=\"info\">未审批</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '1'\" type=\"success\">全部同意</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '2'\" type=\"danger\">已拒绝</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '3'\" type=\"warning\">法诉主管审批</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '4'\" type=\"warning\">总监审批</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '5'\" type=\"warning\">财务主管/总监抄送</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '6'\" type=\"warning\">总经理/董事长审批</el-tag>\r\n            <el-tag v-else type=\"warning\">{{ getStatusText(scope.row.approvalStatus) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\" />\r\n        <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" width=\"100\" />\r\n        <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" />\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleSingleApprove(scope.row, 'approve')\"\r\n              v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:approve']\">\r\n              通过\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleSingleApprove(scope.row, 'reject')\"\r\n              v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:approve']\">\r\n              拒绝\r\n            </el-button>\r\n            <div v-else>\r\n              <!-- 已完成状态 -->\r\n              <el-tag v-if=\"scope.row.approvalStatus == '1'\" type=\"success\" size=\"mini\">全部同意</el-tag>\r\n              <el-tag v-else-if=\"scope.row.approvalStatus == '2'\" type=\"danger\" size=\"mini\">已拒绝</el-tag>\r\n\r\n              <!-- 审批中但权限不足 -->\r\n              <div v-else-if=\"['0', '3', '4', '5', '6'].includes(scope.row.approvalStatus) || scope.row.approvalStatus == null || scope.row.approvalStatus == ''\">\r\n                <el-tag type=\"info\" size=\"mini\">{{ getStatusText(scope.row.approvalStatus) }}</el-tag>\r\n                <div style=\"font-size: 11px; color: #999; margin-top: 2px;\">\r\n                  需要：{{ getRequiredRoleText(scope.row.approvalStatus) }}\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 其他状态 -->\r\n              <el-tag v-else type=\"warning\" size=\"mini\">{{ getStatusText(scope.row.approvalStatus) }}</el-tag>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 单个审批确认对话框 -->\r\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalOpen\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"singleApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"singleApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\r\n        <el-button @click=\"singleApprovalOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量审批确认对话框 -->\r\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalOpen\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"选中记录\">\r\n          <span>{{ selectedRecords.length }} 条记录</span>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"batchApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"batchApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\r\n        <el-button @click=\"batchApprovalOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 贷款人信息对话框 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listLitigationCostApproval,\r\n  getLitigationCostSubmissionRecords,\r\n  singleApproveLitigationCostNew,\r\n  batchApproveLitigationCostNew\r\n} from \"@/api/litigation_cost_approval/litigation_cost_approval\"\r\nimport { checkPermi } from \"@/utils/permission\"\r\nimport areaList from \"../../../assets/area.json\"\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nexport default {\r\n  name: \"Vm_car_order\",\r\n  components: {\r\n    userInfo,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 诉讼费用审批表格数据\r\n      litigationCostApprovalList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        // 严格按照9个筛选条件\r\n        // 1. 贷款人姓名\r\n        customerName: null,\r\n        // 2. 贷款人身份证号\r\n        certId: null,\r\n        // 3. 出单渠道\r\n        jgName: null,\r\n        // 4. 放款银行\r\n        lendingBank: null,\r\n        // 5. 法诉状态(多级)\r\n        litigationStatus: null,\r\n        // 6. 申请人\r\n        applicationBy: null,\r\n        // 7. 费用类型\r\n        costCategory: null,\r\n        // 8. 审批状态\r\n        approvalStatus: null,\r\n        // 9. 申请时间区间\r\n        startTime: null,\r\n        endTime: null,\r\n        // 10. 审批时间区间\r\n        approvalStartTime: null,\r\n        approvalEndTime: null,\r\n      },\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 审批日期范围\r\n      approvalDateRange: [],\r\n      // 表单参数\r\n      form: {\r\n        id:'',\r\n        status: 0,\r\n        rejectReason:null,\r\n      },\r\n      // 当前审批记录\r\n      currentRecord: {},\r\n      // 费用提交记录列表\r\n      submissionRecords: [],\r\n      // 记录加载状态\r\n      recordsLoading: false,\r\n      // 选中的记录\r\n      selectedRecords: [],\r\n      // 单个审批对话框\r\n      singleApprovalOpen: false,\r\n      singleApprovalForm: {\r\n        id: '',\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 批量审批对话框\r\n      batchApprovalOpen: false,\r\n      batchApprovalForm: {\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 贷款人信息相关\r\n      userInfoVisible: false,\r\n      customerInfo: {},\r\n      // 法诉状态多级选项\r\n      litigationStatusOptions: [\r\n        {\r\n          value: '起诉',\r\n          label: '起诉',\r\n          children: [\r\n            { value: '起诉-准备材料', label: '准备材料' },\r\n            { value: '起诉-已提交', label: '已提交' },\r\n            { value: '起诉-法院受理', label: '法院受理' }\r\n          ]\r\n        },\r\n        {\r\n          value: '审理',\r\n          label: '审理',\r\n          children: [\r\n            { value: '审理-开庭审理', label: '开庭审理' },\r\n            { value: '审理-等待判决', label: '等待判决' },\r\n            { value: '审理-一审判决', label: '一审判决' }\r\n          ]\r\n        },\r\n        {\r\n          value: '执行',\r\n          label: '执行',\r\n          children: [\r\n            { value: '执行-申请执行', label: '申请执行' },\r\n            { value: '执行-执行中', label: '执行中' },\r\n            { value: '执行-执行完毕', label: '执行完毕' }\r\n          ]\r\n        },\r\n        {\r\n          value: '结案',\r\n          label: '结案',\r\n          children: [\r\n            { value: '结案-胜诉结案', label: '胜诉结案' },\r\n            { value: '结案-败诉结案', label: '败诉结案' },\r\n            { value: '结案-和解结案', label: '和解结案' }\r\n          ]\r\n        }\r\n      ],\r\n      // 表单校验\r\n      rules: {\r\n        keyProvince:'',\r\n        keyCity:'',\r\n        keyBorough:'',\r\n        keyDetailAddress:'',\r\n      },\r\n      provinceList:areaList,\r\n      cityList:[],\r\n      districtList:[]\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    /** 查询法诉费用审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listLitigationCostApproval(this.queryParams).then(response => {\r\n        this.litigationCostApprovalList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      }).catch(error => {\r\n        console.error('查询失败:', error)\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id:'',\r\n        status: 0,\r\n        rejectReason:null,\r\n      }\r\n      this.currentRecord = {}\r\n      this.submissionRecords = []\r\n      this.selectedRecords = []\r\n      this.singleApprovalOpen = false\r\n      this.batchApprovalOpen = false\r\n      this.singleApprovalForm = {\r\n        id: '',\r\n        action: '',\r\n        rejectReason: ''\r\n      }\r\n      this.batchApprovalForm = {\r\n        action: '',\r\n        rejectReason: ''\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.approvalDateRange = []\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加VIEW\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.currentRecord = row\r\n      this.loadSubmissionRecords(row.litigationCaseId)\r\n      this.open = true\r\n    },\r\n\r\n    /** 批量修改按钮操作 */\r\n    handleBatchEdit() {\r\n      this.$modal.msgError('请选择单条记录进行审批操作')\r\n    },\r\n\r\n    /** 加载费用提交记录 */\r\n    loadSubmissionRecords(litigationCaseId) {\r\n      this.recordsLoading = true\r\n      getLitigationCostSubmissionRecords(litigationCaseId).then(response => {\r\n        this.submissionRecords = response.data || []\r\n        this.recordsLoading = false\r\n      }).catch(() => {\r\n        this.recordsLoading = false\r\n      })\r\n    },\r\n\r\n    /** 记录选择变化 */\r\n    handleRecordSelectionChange(selection) {\r\n      this.selectedRecords = selection\r\n    },\r\n\r\n    /** 单个审批 */\r\n    handleSingleApprove(record, action) {\r\n      this.singleApprovalForm.id = record.id\r\n      this.singleApprovalForm.action = action\r\n      this.singleApprovalForm.rejectReason = ''\r\n      this.singleApprovalOpen = true\r\n    },\r\n\r\n    /** 确认单个审批 */\r\n    confirmSingleApproval() {\r\n      if (this.singleApprovalForm.action === 'reject') {\r\n        this.$refs[\"singleApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeSingleApproval()\r\n        })\r\n      } else {\r\n        this.executeSingleApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行单个审批 */\r\n    executeSingleApproval() {\r\n      const data = {\r\n        id: this.singleApprovalForm.id,\r\n        action: this.singleApprovalForm.action,\r\n        rejectReason: this.singleApprovalForm.rejectReason\r\n      }\r\n\r\n      singleApproveLitigationCostNew(data).then(() => {\r\n        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.singleApprovalOpen = false\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        console.error('审批失败:', error)\r\n      })\r\n    },\r\n\r\n    /** 批量审批 */\r\n    handleBatchApprove(action) {\r\n      if (this.selectedRecords.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      this.batchApprovalForm.action = action\r\n      this.batchApprovalForm.rejectReason = ''\r\n      this.batchApprovalOpen = true\r\n    },\r\n\r\n    /** 确认批量审批 */\r\n    confirmBatchApproval() {\r\n      if (this.batchApprovalForm.action === 'reject') {\r\n        this.$refs[\"batchApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeBatchApproval()\r\n        })\r\n      } else {\r\n        this.executeBatchApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行批量审批 */\r\n    executeBatchApproval() {\r\n      const data = {\r\n        ids: this.selectedRecords.map(record => record.id),\r\n        action: this.batchApprovalForm.action,\r\n        rejectReason: this.batchApprovalForm.rejectReason\r\n      }\r\n\r\n      batchApproveLitigationCostNew(data).then(() => {\r\n        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.batchApprovalOpen = false\r\n        this.selectedRecords = []\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        console.error('批量审批失败:', error)\r\n      })\r\n    },\r\n\r\n    /** 主列表批量审批 */\r\n    handleBatchApproveMain(status) {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      const statusText = status === '0' ? '通过' : '拒绝'\r\n      this.$modal.confirm(`确认要批量${statusText}选中的 ${this.ids.length} 条记录吗？`).then(() => {\r\n        const data = {\r\n          ids: this.ids,\r\n          action: status === '0' ? 'approve' : 'reject',\r\n          rejectReason: status === '1' ? '批量拒绝' : ''\r\n        }\r\n\r\n        return batchApproveLitigationCostNew(data)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(`批量${statusText}成功`)\r\n        this.getList()\r\n      }).catch(() => {})\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete() {\r\n      this.$modal.confirm('是否确认删除选中的数据项？').then(() => {\r\n        // 这里可以调用删除API，暂时只是提示\r\n        this.$modal.msgSuccess(\"删除功能暂未实现\")\r\n      }).catch(() => {})\r\n    },\r\n\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('litigation_cost_approval/litigation_cost_approval/export', {\r\n        ...this.queryParams\r\n      }, `litigation_cost_approval_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    /** 打开贷款人信息 */\r\n    openUserInfo(row) {\r\n      if (!row.customerId && !row.applyId) {\r\n        this.$modal.msgError('无法获取贷款人信息')\r\n        return\r\n      }\r\n\r\n      this.customerInfo = {\r\n        customerId: row.customerId,\r\n        applyId: row.applyId,\r\n        customerName: row.customerName\r\n      }\r\n      this.userInfoVisible = true\r\n    },\r\n\r\n    /** 处理日期范围变化 */\r\n    handleDateRangeChange(dates) {\r\n      if (dates && dates.length === 2) {\r\n        this.queryParams.startTime = dates[0]\r\n        this.queryParams.endTime = dates[1]\r\n      } else {\r\n        this.queryParams.startTime = null\r\n        this.queryParams.endTime = null\r\n      }\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 处理审批时间范围变化 */\r\n    handleApprovalDateRangeChange(dates) {\r\n      if (dates && dates.length === 2) {\r\n        this.queryParams.approvalStartTime = dates[0]\r\n        this.queryParams.approvalEndTime = dates[1]\r\n      } else {\r\n        this.queryParams.approvalStartTime = null\r\n        this.queryParams.approvalEndTime = null\r\n      }\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        '0': '未审批',\r\n        '1': '全部同意',\r\n        '2': '已拒绝',\r\n        '3': '法诉主管审批',\r\n        '4': '总监审批',\r\n        '5': '财务主管/总监抄送',\r\n        '6': '总经理/董事长审批'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n\r\n    /** 检查记录是否可以审批 */\r\n    canApproveRecord(record) {\r\n      // 首先检查用户是否有审批权限\r\n      if (!checkPermi(['litigation_cost_approval:litigation_cost_approval:approve'])) {\r\n        return false\r\n      }\r\n\r\n      // 已完成的状态不能再审批\r\n      if (record.approvalStatus == '1' || record.approvalStatus == '2') {\r\n        return false\r\n      }\r\n\r\n      // 检查用户角色是否匹配当前审批状态\r\n      return this.canUserApproveStatus(record.approvalStatus)\r\n    },\r\n\r\n    /** 检查用户角色是否可以审批当前状态 */\r\n    canUserApproveStatus(status) {\r\n      const roles = this.$store.getters.roles || []\r\n\r\n      // 过滤掉Vue的Observer对象，只保留字符串角色\r\n      const cleanRoles = roles.filter(role => typeof role === 'string')\r\n\r\n      // 管理员拥有所有权限\r\n      if (cleanRoles.includes('admin')) {\r\n        return true\r\n      }\r\n\r\n      // 检查角色名称是否匹配\r\n      const hasRole = (targetRole) => {\r\n        return cleanRoles.some(role => role === targetRole)\r\n      }\r\n\r\n      // 处理空状态（null、undefined、空字符串）\r\n      if (status == null || status === undefined || status === '') {\r\n        return hasRole('法诉主管') || hasRole('litigation_supervisor') || hasRole('judicial_director')\r\n      }\r\n\r\n      let result = false\r\n      switch (status) {\r\n        case '0': // 未审批\r\n          // 未审批状态由法诉主管审批\r\n          result = hasRole('法诉主管') || hasRole('litigation_supervisor') || hasRole('judicial_director')\r\n          break\r\n        case '3': // 法诉主管审批状态\r\n          // 法诉主管审批状态由总监审批\r\n          result = hasRole('总监') || hasRole('director')\r\n          break\r\n        case '4': // 总监审批状态\r\n          // 总监审批状态由财务主管/总监审批\r\n          result = hasRole('财务主管') || hasRole('财务总监') || hasRole('finance_supervisor') || hasRole('finance_director')\r\n          break\r\n        case '5': // 财务主管/总监抄送状态\r\n          // 财务主管/总监抄送状态由总经理/董事长审批\r\n          result = hasRole('总经理') || hasRole('董事长') || hasRole('general_manager') || hasRole('chairman')\r\n          break\r\n        case '6': // 总经理/董事长审批状态\r\n          // 总经理/董事长审批状态已经是最后一级，不允许再审批\r\n          result = false\r\n          break\r\n        default:\r\n          result = false\r\n      }\r\n\r\n      return result\r\n    },\r\n\r\n    /** 获取当前状态需要的审批角色文本 */\r\n    getRequiredRoleText(status) {\r\n      switch (status) {\r\n        case '0': // 未审批\r\n        case null:\r\n        case '':\r\n          return '法诉主管'\r\n        case '3': // 法诉主管审批状态\r\n          return '总监'\r\n        case '4': // 总监审批状态\r\n          return '财务主管/总监'\r\n        case '5': // 财务主管/总监抄送状态\r\n          return '总经理/董事长'\r\n        case '6': // 总经理/董事长审批状态\r\n          return '已完成'\r\n        default:\r\n          return '未知'\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.approval-header {\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-approval-section {\r\n  border: 1px solid #e4e7ed;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 10px;\r\n}\r\n\r\n.el-tag {\r\n  margin: 2px;\r\n}\r\n</style>\r\n"]}]}