{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue?vue&type=template&id=7cd31f38&scoped=true", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue", "mtime": 1754298418754}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753353054666}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}